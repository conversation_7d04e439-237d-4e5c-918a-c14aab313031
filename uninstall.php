<?php
/**
 * BDPay Uninstall
 *
 * Uninstalling BDPay deletes user data, tables, and options.
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'WP_UNINSTALL_PLUGIN' ) || exit;

// Security check
if ( ! defined( 'WP_UNINSTALL_PLUGIN' ) ) {
    exit;
}

/**
 * Only remove ALL plugin data if BDPAY_REMOVE_ALL_DATA constant is set to true in user's
 * wp-config.php. This is to prevent data loss when deleting the plugin from the backend
 * and to ensure only the site owner can perform this action.
 */
if ( defined( 'BDPAY_REMOVE_ALL_DATA' ) && true === BDPAY_REMOVE_ALL_DATA ) {
    
    global $wpdb;

    // Delete plugin options
    $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE 'bdpay_%'" );

    // Delete plugin transients
    $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_bdpay_%'" );
    $wpdb->query( "DELETE FROM {$wpdb->options} WHERE option_name LIKE '_transient_timeout_bdpay_%'" );

    // Delete plugin tables
    $wpdb->query( "DROP TABLE IF EXISTS {$wpdb->prefix}bdpay_transactions" );

    // Delete plugin user meta
    $wpdb->query( "DELETE FROM {$wpdb->usermeta} WHERE meta_key LIKE 'bdpay_%'" );

    // Delete plugin post meta
    $wpdb->query( "DELETE FROM {$wpdb->postmeta} WHERE meta_key LIKE '_bdpay_%'" );

    // Delete plugin posts (if any custom post types were created)
    $wpdb->query( "DELETE FROM {$wpdb->posts} WHERE post_type LIKE 'bdpay_%'" );

    // Clear any cached data that has been removed
    wp_cache_flush();
}
