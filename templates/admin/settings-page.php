<?php
/**
 * BDPay admin settings page template
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

?>

<div class="wrap bdpay-settings-wrap">
    <h1><?php _e( 'BDPay Settings', 'bdpay' ); ?></h1>
    <p class="bdpay-settings-description"><?php _e( 'Configure your mobile payment gateway settings and payment methods.', 'bdpay' ); ?></p>

    <form method="post" action="">
        <?php wp_nonce_field( 'bdpay_settings', 'bdpay_settings_nonce' ); ?>

        <nav class="nav-tab-wrapper">
            <a href="#general" class="nav-tab nav-tab-active"><?php _e( 'General', 'bdpay' ); ?></a>
            <a href="#payment-methods" class="nav-tab"><?php _e( 'Payment Methods', 'bdpay' ); ?></a>
        </nav>

        <div class="bdpay-tab-content">
            <!-- General Settings Tab -->
            <div id="general" class="bdpay-tab-pane active">
                <h2><?php _e( 'General Settings', 'bdpay' ); ?></h2>

                <div class="bdpay-settings-section">
                    <div class="bdpay-setting-row">
                        <div class="bdpay-setting-label">
                            <label><?php _e( 'Screenshot Upload', 'bdpay' ); ?></label>
                        </div>
                        <div class="bdpay-setting-field">
                            <label class="bdpay-checkbox-label">
                                <input type="checkbox" name="bdpay_enable_screenshot" value="yes"
                                       <?php checked( get_option( 'bdpay_enable_screenshot', 'no' ), 'yes' ); ?>>
                                <?php _e( 'Allow customers to upload payment screenshots', 'bdpay' ); ?>
                            </label>
                            <p class="bdpay-field-description"><?php _e( 'Customers can upload screenshots of their payment confirmation for faster verification.', 'bdpay' ); ?></p>
                        </div>
                    </div>

                    <div class="bdpay-setting-row">
                        <div class="bdpay-setting-label">
                            <label><?php _e( 'Email Notifications', 'bdpay' ); ?></label>
                        </div>
                        <div class="bdpay-setting-field">
                            <label class="bdpay-checkbox-label">
                                <input type="checkbox" name="bdpay_enable_notifications" value="yes"
                                       <?php checked( get_option( 'bdpay_enable_notifications', 'yes' ), 'yes' ); ?>>
                                <?php _e( 'Send email notifications for payment submissions and verifications', 'bdpay' ); ?>
                            </label>
                            <p class="bdpay-field-description"><?php _e( 'Both admin and customers will receive email notifications about payment status changes.', 'bdpay' ); ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Payment Methods Tab -->
            <div id="payment-methods" class="bdpay-tab-pane">
                <h2><?php _e( 'Payment Methods Configuration', 'bdpay' ); ?></h2>
                <p class="bdpay-tab-description"><?php _e( 'Configure the mobile financial services available to your customers.', 'bdpay' ); ?></p>

                <div class="bdpay-payment-methods">
                    <?php foreach ( $payment_methods as $method => $label ) : ?>
                        <div class="bdpay-payment-method">
                            <div class="bdpay-method-header">
                                <label class="bdpay-method-toggle">
                                    <input type="checkbox" name="bdpay_enable_<?php echo esc_attr( $method ); ?>" value="yes"
                                           <?php checked( get_option( 'bdpay_enable_' . $method, 'no' ), 'yes' ); ?>>
                                    <span class="bdpay-method-name"><?php echo esc_html( $label ); ?></span>
                                </label>
                            </div>

                            <div class="bdpay-method-fields">
                                <div class="bdpay-field-group">
                                    <label for="bdpay_wallet_<?php echo esc_attr( $method ); ?>" class="bdpay-field-label">
                                        <?php printf( __( '%s Wallet Number', 'bdpay' ), $label ); ?>
                                    </label>
                                    <input type="text"
                                           name="bdpay_wallet_<?php echo esc_attr( $method ); ?>"
                                           id="bdpay_wallet_<?php echo esc_attr( $method ); ?>"
                                           value="<?php echo esc_attr( get_option( 'bdpay_wallet_' . $method, '' ) ); ?>"
                                           class="bdpay-text-input"
                                           placeholder="<?php _e( 'e.g., 01XXXXXXXXX', 'bdpay' ); ?>">
                                    <p class="bdpay-field-description">
                                        <?php printf( __( 'Enter your %s wallet number where customers will send payments.', 'bdpay' ), $label ); ?>
                                    </p>
                                </div>

                                <div class="bdpay-field-group">
                                    <label for="bdpay_instructions_<?php echo esc_attr( $method ); ?>" class="bdpay-field-label">
                                        <?php printf( __( '%s Instructions', 'bdpay' ), $label ); ?>
                                    </label>
                                    <textarea name="bdpay_instructions_<?php echo esc_attr( $method ); ?>"
                                              id="bdpay_instructions_<?php echo esc_attr( $method ); ?>"
                                              rows="4"
                                              class="bdpay-textarea"
                                              placeholder="<?php _e( 'Step-by-step payment instructions for customers...', 'bdpay' ); ?>"><?php echo esc_textarea( get_option( 'bdpay_instructions_' . $method, '' ) ); ?></textarea>
                                    <p class="bdpay-field-description">
                                        <?php printf( __( 'Provide step-by-step instructions for customers on how to send money via %s.', 'bdpay' ), $label ); ?>
                                    </p>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>

        <?php submit_button( __( 'Save Settings', 'bdpay' ) ); ?>
    </form>
</div>

<style>
/* Simple BDPay Settings Styles */
.bdpay-settings-wrap {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.bdpay-settings-description {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

.nav-tab-wrapper {
    margin-bottom: 0;
}

.bdpay-tab-content {
    background: #fff;
    padding: 20px;
    border: 1px solid #ddd;
    border-top: none;
    border-radius: 0 0 4px 4px;
}

.bdpay-tab-pane {
    display: none;
}

.bdpay-tab-pane.active {
    display: block;
}

.bdpay-tab-description {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
}

/* General Settings */
.bdpay-settings-section {
    max-width: 800px;
}

.bdpay-setting-row {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.bdpay-setting-row:last-child {
    border-bottom: none;
    margin-bottom: 0;
    padding-bottom: 0;
}

.bdpay-setting-label {
    width: 200px;
    flex-shrink: 0;
    padding-right: 20px;
}

.bdpay-setting-label label {
    font-weight: 600;
    color: #333;
}

.bdpay-setting-field {
    flex: 1;
}

.bdpay-checkbox-label {
    display: flex;
    align-items: center;
    gap: 8px;
    font-size: 14px;
    cursor: pointer;
}

.bdpay-field-description {
    margin: 8px 0 0 0;
    color: #666;
    font-size: 13px;
    line-height: 1.4;
}

/* Payment Methods */
.bdpay-payment-methods {
    max-width: 800px;
}

.bdpay-payment-method {
    margin-bottom: 20px;
    border: 1px solid #ddd;
    border-radius: 4px;
    background: #fff;
}

.bdpay-method-header {
    padding: 16px 20px;
    background: #f9f9f9;
    border-bottom: 1px solid #ddd;
}

.bdpay-method-toggle {
    display: flex;
    align-items: center;
    gap: 10px;
    cursor: pointer;
    margin: 0;
}

.bdpay-method-name {
    font-weight: 600;
    color: #333;
    font-size: 16px;
}

.bdpay-method-fields {
    padding: 20px;
}

.bdpay-field-group {
    margin-bottom: 20px;
}

.bdpay-field-group:last-child {
    margin-bottom: 0;
}

.bdpay-field-label {
    display: block;
    margin-bottom: 6px;
    font-weight: 600;
    color: #333;
    font-size: 14px;
}

.bdpay-text-input,
.bdpay-textarea {
    width: 100%;
    max-width: 400px;
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    font-family: inherit;
    transition: border-color 0.2s ease;
}

.bdpay-text-input:focus,
.bdpay-textarea:focus {
    outline: none;
    border-color: #0073aa;
}

.bdpay-textarea {
    resize: vertical;
    min-height: 80px;
}

/* Responsive */
@media (max-width: 768px) {
    .bdpay-setting-row {
        flex-direction: column;
    }

    .bdpay-setting-label {
        width: auto;
        padding-right: 0;
        margin-bottom: 8px;
    }

    .bdpay-text-input,
    .bdpay-textarea {
        max-width: 100%;
    }
}
</style>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Tab switching
    $('.nav-tab').on('click', function(e) {
        e.preventDefault();

        var target = $(this).attr('href');

        // Update tab appearance
        $('.nav-tab').removeClass('nav-tab-active');
        $(this).addClass('nav-tab-active');

        // Show/hide content
        $('.bdpay-tab-pane').removeClass('active');
        $(target).addClass('active');
    });

    // Enable/disable method settings based on checkbox
    $('input[name^="bdpay_enable_"]').on('change', function() {
        var $fields = $(this).closest('.bdpay-payment-method').find('.bdpay-method-fields');

        if ($(this).is(':checked')) {
            $fields.show();
        } else {
            $fields.hide();
        }
    }).trigger('change');
});
</script>
