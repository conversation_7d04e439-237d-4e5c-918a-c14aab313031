<?php
/**
 * Plugin Name: BDPay - Manual Mobile Payment Gateway
 * Plugin URI: https://wordpress.org/plugins/bdpay/
 * Description: Manual mobile payment gateway for Bangladesh supporting bKash, Nagad, Rocket, Upay, and SureCash. Perfect for WooCommerce stores accepting manual MFS payments.
 * Version: 1.0.0
 * Author: M<PERSON><PERSON> <PERSON><PERSON>
 * Author URI: https://mdnahidhasan.netlify.app
 * Text Domain: BDPay
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.8
 * Requires PHP: 7.4
 * License: GPLv2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * WC requires at least: 3.0
 * WC tested up to: 8.4
 *
 * @package BDPay
 * <AUTHOR>
 * @copyright 2025 Md. <PERSON><PERSON>
 * @license GPL-2.0-or-later
 * @since 1.0.0
 */
// Prevent direct access
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Define plugin constants
define( 'BDPAY_VERSION', '1.0.0' );
define( 'BDPAY_PLUGIN_FILE', __FILE__ );
define( 'BDPAY_PLUGIN_BASENAME', plugin_basename( __FILE__ ) );
define( 'BDPAY_PLUGIN_PATH', plugin_dir_path( __FILE__ ) );
define( 'BDPAY_PLUGIN_URL', plugin_dir_url( __FILE__ ) );
define( 'BDPAY_INCLUDES_PATH', BDPAY_PLUGIN_PATH . 'includes/' );
define( 'BDPAY_TEMPLATES_PATH', BDPAY_PLUGIN_PATH . 'templates/' );
define( 'BDPAY_ASSETS_URL', BDPAY_PLUGIN_URL . 'assets/' );
define( 'BDPAY_TEMPLATE_DEBUG_MODE', false );

/**
 * Main BDPay Class
 *
 * @class BDPay
 * @version 1.0.0
 */
final class BDPay {

    /**
     * BDPay version.
     *
     * @var string
     */
    public $version = '1.0.0';

    /**
     * The single instance of the class.
     *
     * @var BDPay
     * @since 1.0.0
     */
    protected static $_instance = null;

    /**
     * Main BDPay Instance.
     *
     * Ensures only one instance of BDPay is loaded or can be loaded.
     *
     * @since 1.0.0
     * @static
     * @return BDPay - Main instance.
     */
    public static function instance() {
        if ( is_null( self::$_instance ) ) {
            self::$_instance = new self();
        }
        return self::$_instance;
    }

    /**
     * BDPay Constructor.
     */
    public function __construct() {
        $this->define_constants();
        $this->includes();
        $this->init_hooks();
    }

    /**
     * Define BDPay Constants.
     */
    private function define_constants() {
        $this->define( 'BDPAY_ABSPATH', dirname( BDPAY_PLUGIN_FILE ) . '/' );
    }

    /**
     * Define constant if not already set.
     *
     * @param string $name  Constant name.
     * @param string|bool $value Constant value.
     */
    private function define( $name, $value ) {
        if ( ! defined( $name ) ) {
            define( $name, $value );
        }
    }

    /**
     * What type of request is this?
     *
     * @param  string $type admin, ajax, cron or frontend.
     * @return bool
     */
    private function is_request( $type ) {
        switch ( $type ) {
            case 'admin':
                return is_admin();
            case 'ajax':
                return defined( 'DOING_AJAX' );
            case 'cron':
                return defined( 'DOING_CRON' );
            case 'frontend':
                return ( ! is_admin() || defined( 'DOING_AJAX' ) ) && ! defined( 'DOING_CRON' );
        }
    }

    /**
     * Include required core files used in admin and on the frontend.
     */
    public function includes() {
        /**
         * Core functions.
         */
        include_once BDPAY_INCLUDES_PATH . 'bdpay-functions.php';

        /**
         * Core classes.
         */
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-install.php';
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-core.php';
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-ajax.php';
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-assets.php';
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-transaction.php';
        include_once BDPAY_INCLUDES_PATH . 'class-bdpay-security.php';

        if ( $this->is_request( 'admin' ) ) {
            include_once BDPAY_INCLUDES_PATH . 'admin/class-bdpay-admin.php';
            include_once BDPAY_INCLUDES_PATH . 'admin/class-bdpay-admin-settings.php';
        }

        if ( $this->is_request( 'frontend' ) ) {
            include_once BDPAY_INCLUDES_PATH . 'class-bdpay-frontend.php';
        }

        // WooCommerce integration is handled in BDPay_Core class
    }

    /**
     * Hook into actions and filters.
     *
     * @since 1.0.0
     */
    private function init_hooks() {
        register_activation_hook( __FILE__, array( 'BDPay_Install', 'install' ) );
        register_deactivation_hook( __FILE__, array( 'BDPay_Install', 'deactivate' ) );

        add_action( 'init', array( $this, 'init' ), 0 );
        add_action( 'plugins_loaded', array( $this, 'plugins_loaded' ) );
        add_action( 'before_woocommerce_init', array( $this, 'declare_woocommerce_compatibility' ) );

        // Add admin notice for WooCommerce dependency
        add_action( 'admin_notices', array( $this, 'woocommerce_missing_notice' ) );
    }

    /**
     * Init BDPay when WordPress Initialises.
     */
    public function init() {
        // Before init action.
        do_action( 'before_bdpay_mmpg_init' );

        // Init action.
        do_action( 'bdpay_mmpg_init' );
    }

    /**
     * When WP has loaded all plugins, trigger the `bdpay_loaded` hook.
     *
     * This ensures `bdpay_loaded` is called only after all other plugins
     * are loaded.
     *
     * @since 1.0.0
     */
    public function plugins_loaded() {
        do_action( 'bdpay_loaded' );
    }



    /**
     * Get the plugin url.
     *
     * @return string
     */
    public function plugin_url() {
        return untrailingslashit( plugins_url( '/', BDPAY_PLUGIN_FILE ) );
    }

    /**
     * Get the plugin path.
     *
     * @return string
     */
    public function plugin_path() {
        return untrailingslashit( plugin_dir_path( BDPAY_PLUGIN_FILE ) );
    }

    /**
     * Get the template path.
     *
     * @return string
     */
    public function template_path() {
        return apply_filters( 'bdpay_template_path', 'bdpay/' );
    }

    /**
     * Get Ajax URL.
     *
     * @return string
     */
    public function ajax_url() {
        return admin_url( 'admin-ajax.php', 'relative' );
    }

    /**
     * WooCommerce missing notice.
     */
    public function woocommerce_missing_notice() {
        if ( ! class_exists( 'WooCommerce' ) ) {
            $class = 'notice notice-error';
            $message = esc_html__( 'BDPay requires WooCommerce to be installed and active.', 'BDPay' );
            printf( '<div class="%1$s"><p>%2$s</p></div>', esc_attr( $class ), esc_html( $message ) );
        }
    }

    /**
     * Declare WooCommerce feature compatibility.
     *
     * @since 1.0.0
     */
    public function declare_woocommerce_compatibility() {
        if ( class_exists( '\Automattic\WooCommerce\Utilities\FeaturesUtil' ) ) {
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'custom_order_tables', __FILE__, true );
            \Automattic\WooCommerce\Utilities\FeaturesUtil::declare_compatibility( 'cart_checkout_blocks', __FILE__, false );
        }
    }
}

/**
 * Main instance of BDPay.
 *
 * Returns the main instance of BDPay to prevent the need to use globals.
 *
 * @since  1.0.0
 * @return BDPay
 */
function bdpay() {
    return BDPay::instance();
}

// Global for backwards compatibility.
$GLOBALS['bdpay'] = bdpay();
