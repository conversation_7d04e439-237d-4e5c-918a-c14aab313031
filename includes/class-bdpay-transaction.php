<?php
/**
 * BDPay Transaction Class
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Transaction Class.
 */
class BDPay_Transaction {

    /**
     * Transaction ID.
     *
     * @var int
     */
    protected $id = 0;

    /**
     * Transaction data.
     *
     * @var array
     */
    protected $data = array(
        'order_id'       => 0,
        'transaction_id' => '',
        'sender_phone'   => '',
        'payment_method' => '',
        'amount'         => 0.00,
        'status'         => 'pending',
        'screenshot_url' => '',
        'notes'          => '',
        'date_created'   => null,
        'date_modified'  => null,
    );

    /**
     * Constructor.
     *
     * @param int $id Transaction ID.
     */
    public function __construct( $id = 0 ) {
        if ( $id > 0 ) {
            $this->set_id( $id );
            $this->read();
        }
    }

    /**
     * Set transaction ID.
     *
     * @param int $id
     */
    public function set_id( $id ) {
        $this->id = absint( $id );
    }

    /**
     * Get transaction ID.
     *
     * @return int
     */
    public function get_id() {
        return $this->id;
    }

    /**
     * Set order ID.
     *
     * @param int $order_id
     */
    public function set_order_id( $order_id ) {
        $this->data['order_id'] = absint( $order_id );
    }

    /**
     * Get order ID.
     *
     * @return int
     */
    public function get_order_id() {
        return $this->data['order_id'];
    }

    /**
     * Set transaction ID.
     *
     * @param string $transaction_id
     */
    public function set_transaction_id( $transaction_id ) {
        $this->data['transaction_id'] = sanitize_text_field( $transaction_id );
    }

    /**
     * Get transaction ID.
     *
     * @return string
     */
    public function get_transaction_id() {
        return $this->data['transaction_id'];
    }

    /**
     * Set sender phone.
     *
     * @param string $phone
     */
    public function set_sender_phone( $phone ) {
        $this->data['sender_phone'] = sanitize_text_field( $phone );
    }

    /**
     * Get sender phone.
     *
     * @return string
     */
    public function get_sender_phone() {
        return $this->data['sender_phone'];
    }

    /**
     * Set payment method.
     *
     * @param string $method
     */
    public function set_payment_method( $method ) {
        $this->data['payment_method'] = sanitize_text_field( $method );
    }

    /**
     * Get payment method.
     *
     * @return string
     */
    public function get_payment_method() {
        return $this->data['payment_method'];
    }

    /**
     * Set amount.
     *
     * @param float $amount
     */
    public function set_amount( $amount ) {
        $this->data['amount'] = floatval( $amount );
    }

    /**
     * Get amount.
     *
     * @return float
     */
    public function get_amount() {
        return $this->data['amount'];
    }

    /**
     * Set status.
     *
     * @param string $status
     */
    public function set_status( $status ) {
        $this->data['status'] = sanitize_text_field( $status );
    }

    /**
     * Get status.
     *
     * @return string
     */
    public function get_status() {
        return $this->data['status'];
    }

    /**
     * Set screenshot URL.
     *
     * @param string $url
     */
    public function set_screenshot_url( $url ) {
        $this->data['screenshot_url'] = esc_url_raw( $url );
    }

    /**
     * Get screenshot URL.
     *
     * @return string
     */
    public function get_screenshot_url() {
        return $this->data['screenshot_url'];
    }

    /**
     * Set notes.
     *
     * @param string $notes
     */
    public function set_notes( $notes ) {
        $this->data['notes'] = sanitize_textarea_field( $notes );
    }

    /**
     * Get notes.
     *
     * @return string
     */
    public function get_notes() {
        return $this->data['notes'];
    }

    /**
     * Get date created.
     *
     * @return string
     */
    public function get_date_created() {
        return $this->data['date_created'];
    }

    /**
     * Get date modified.
     *
     * @return string
     */
    public function get_date_modified() {
        return $this->data['date_modified'];
    }

    /**
     * Read transaction data from database.
     */
    protected function read() {
        global $wpdb;

        $data = $wpdb->get_row( $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}bdpay_transactions WHERE id = %d",
            $this->id
        ), ARRAY_A );

        if ( $data ) {
            $this->data = array_merge( $this->data, $data );
        }
    }

    /**
     * Create new transaction.
     *
     * @param array $data
     * @return bool
     */
    public function create( $data = array() ) {
        global $wpdb;

        if ( ! empty( $data ) ) {
            foreach ( $data as $key => $value ) {
                if ( array_key_exists( $key, $this->data ) ) {
                    $this->data[ $key ] = $value;
                }
            }
        }

        $result = $wpdb->insert(
            $wpdb->prefix . 'bdpay_transactions',
            array(
                'order_id'       => $this->data['order_id'],
                'transaction_id' => $this->data['transaction_id'],
                'sender_phone'   => $this->data['sender_phone'],
                'payment_method' => $this->data['payment_method'],
                'amount'         => $this->data['amount'],
                'status'         => $this->data['status'],
                'screenshot_url' => $this->data['screenshot_url'],
                'notes'          => $this->data['notes'],
                'date_created'   => current_time( 'mysql' ),
                'date_modified'  => current_time( 'mysql' ),
            ),
            array( '%d', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%s', '%s' )
        );

        if ( $result ) {
            $this->set_id( $wpdb->insert_id );
            $this->read();
            return true;
        }

        return false;
    }

    /**
     * Update transaction.
     *
     * @param array $data
     * @return bool
     */
    public function update( $data = array() ) {
        global $wpdb;

        if ( ! $this->id ) {
            return false;
        }

        if ( ! empty( $data ) ) {
            foreach ( $data as $key => $value ) {
                if ( array_key_exists( $key, $this->data ) ) {
                    $this->data[ $key ] = $value;
                }
            }
        }

        $result = $wpdb->update(
            $wpdb->prefix . 'bdpay_transactions',
            array(
                'order_id'       => $this->data['order_id'],
                'transaction_id' => $this->data['transaction_id'],
                'sender_phone'   => $this->data['sender_phone'],
                'payment_method' => $this->data['payment_method'],
                'amount'         => $this->data['amount'],
                'status'         => $this->data['status'],
                'screenshot_url' => $this->data['screenshot_url'],
                'notes'          => $this->data['notes'],
                'date_modified'  => current_time( 'mysql' ),
            ),
            array( 'id' => $this->id ),
            array( '%d', '%s', '%s', '%s', '%f', '%s', '%s', '%s', '%s' ),
            array( '%d' )
        );

        if ( false !== $result ) {
            $this->read();
            return true;
        }

        return false;
    }

    /**
     * Update transaction status.
     *
     * @param string $status
     * @return bool
     */
    public function update_status( $status ) {
        return $this->update( array( 'status' => $status ) );
    }

    /**
     * Delete transaction.
     *
     * @return bool
     */
    public function delete() {
        global $wpdb;

        if ( ! $this->id ) {
            return false;
        }

        $result = $wpdb->delete(
            $wpdb->prefix . 'bdpay_transactions',
            array( 'id' => $this->id ),
            array( '%d' )
        );

        if ( $result ) {
            $this->id = 0;
            $this->data = array();
            return true;
        }

        return false;
    }

    /**
     * Get transaction by order ID.
     *
     * @param int $order_id
     * @return BDPay_Transaction|null
     */
    public static function get_by_order_id( $order_id ) {
        global $wpdb;

        $id = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}bdpay_transactions WHERE order_id = %d ORDER BY id DESC LIMIT 1",
            $order_id
        ) );

        if ( $id ) {
            return new self( $id );
        }

        return null;
    }

    /**
     * Get transaction by transaction ID.
     *
     * @param string $transaction_id
     * @return BDPay_Transaction|null
     */
    public static function get_by_transaction_id( $transaction_id ) {
        global $wpdb;

        $id = $wpdb->get_var( $wpdb->prepare(
            "SELECT id FROM {$wpdb->prefix}bdpay_transactions WHERE transaction_id = %s ORDER BY id DESC LIMIT 1",
            $transaction_id
        ) );

        if ( $id ) {
            return new self( $id );
        }

        return null;
    }

    /**
     * Get all transactions.
     *
     * @param array $args
     * @return array
     */
    public static function get_transactions( $args = array() ) {
        global $wpdb;

        $defaults = array(
            'status'   => '',
            'order_by' => 'date_created',
            'order'    => 'DESC',
            'limit'    => 20,
            'offset'   => 0,
        );

        $args = wp_parse_args( $args, $defaults );

        $where = '1=1';
        $where_values = array();

        if ( ! empty( $args['status'] ) ) {
            $where .= ' AND status = %s';
            $where_values[] = $args['status'];
        }

        $query = $wpdb->prepare(
            "SELECT * FROM {$wpdb->prefix}bdpay_transactions WHERE {$where} ORDER BY {$args['order_by']} {$args['order']} LIMIT %d OFFSET %d",
            array_merge( $where_values, array( $args['limit'], $args['offset'] ) )
        );

        $results = $wpdb->get_results( $query, ARRAY_A );
        $transactions = array();

        foreach ( $results as $result ) {
            $transaction = new self();
            $transaction->set_id( $result['id'] );
            $transaction->data = $result;
            $transactions[] = $transaction;
        }

        return $transactions;
    }
}
