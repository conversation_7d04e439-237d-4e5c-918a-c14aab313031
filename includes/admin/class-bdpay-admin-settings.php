<?php
/**
 * BDPay Admin Settings
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Admin_Settings Class.
 */
class BDPay_Admin_Settings {

    /**
     * Setting pages.
     *
     * @var array
     */
    private static $settings = array();

    /**
     * Error messages.
     *
     * @var array
     */
    private static $errors = array();

    /**
     * Update messages.
     *
     * @var array
     */
    private static $messages = array();

    /**
     * Include the settings page classes.
     */
    public static function get_settings_pages() {
        if ( empty( self::$settings ) ) {
            $settings = array();

            // For now, we'll use the simple admin settings page
            // TODO: Implement advanced settings classes if needed

            self::$settings = apply_filters( 'bdpay_get_settings_pages', $settings );
        }

        return self::$settings;
    }

    /**
     * Save the settings.
     */
    public static function save() {
        global $current_tab, $current_section;

        check_admin_referer( 'bdpay-settings' );

        // Trigger actions
        do_action( 'bdpay_settings_save_' . $current_tab );
        do_action( 'bdpay_update_options_' . $current_tab );
        do_action( 'bdpay_update_options' );

        self::add_message( __( 'Your settings have been saved.', 'bdpay' ) );

        // Clear any unwanted data and flush rules
        delete_transient( 'bdpay_cache_excluded_uris' );

        do_action( 'bdpay_settings_saved' );
    }

    /**
     * Add a message.
     *
     * @param string $text
     */
    public static function add_message( $text ) {
        self::$messages[] = $text;
    }

    /**
     * Add an error.
     *
     * @param string $text
     */
    public static function add_error( $text ) {
        self::$errors[] = $text;
    }

    /**
     * Output messages + errors.
     */
    public static function show_messages() {
        if ( count( self::$errors ) > 0 ) {
            foreach ( self::$errors as $error ) {
                echo '<div id="message" class="error inline"><p><strong>' . esc_html( $error ) . '</strong></p></div>';
            }
        } elseif ( count( self::$messages ) > 0 ) {
            foreach ( self::$messages as $message ) {
                echo '<div id="message" class="updated inline"><p><strong>' . esc_html( $message ) . '</strong></p></div>';
            }
        }
    }

    /**
     * Settings page.
     *
     * Handles the display of the main settings page in admin.
     */
    public static function output() {
        global $current_section, $current_tab;

        $suffix = defined( 'SCRIPT_DEBUG' ) && SCRIPT_DEBUG ? '' : '.min';

        do_action( 'bdpay_settings_start' );

        wp_enqueue_script( 'bdpay_settings', BDPAY_ASSETS_URL . 'js/admin/settings' . $suffix . '.js', array( 'jquery', 'jquery-ui-datepicker', 'jquery-ui-sortable', 'iris' ), BDPAY_VERSION );

        wp_localize_script(
            'bdpay_settings',
            'bdpay_settings_params',
            array(
                'i18n_nav_warning' => __( 'The changes you made will be lost if you navigate away from this page.', 'bdpay' ),
            )
        );

        // Include settings pages
        self::get_settings_pages();

        // Get current tab/section
        $current_tab     = empty( $_GET['tab'] ) ? 'general' : sanitize_title( wp_unslash( $_GET['tab'] ) );
        $current_section = empty( $_REQUEST['section'] ) ? '' : sanitize_title( wp_unslash( $_REQUEST['section'] ) );

        // Save settings if data has been posted
        if ( ! empty( $_POST ) ) {
            self::save();
        }

        // Add any posted messages
        if ( ! empty( $_GET['bdpay_error'] ) ) {
            self::add_error( wp_kses_post( wp_unslash( $_GET['bdpay_error'] ) ) );
        }

        if ( ! empty( $_GET['bdpay_message'] ) ) {
            self::add_message( wp_kses_post( wp_unslash( $_GET['bdpay_message'] ) ) );
        }

        self::get_settings_pages();

        // Get tabs for the settings page
        $tabs = apply_filters( 'bdpay_settings_tabs_array', array() );

        // For now, we'll use the simple settings page
        // TODO: Create html-admin-settings.php template if advanced settings are needed
        echo '<div class="wrap"><h1>' . __( 'BDPay Settings', 'bdpay' ) . '</h1>';
        echo '<p>' . __( 'Please use the BDPay settings page under WooCommerce menu.', 'bdpay' ) . '</p>';
        echo '</div>';
    }

    /**
     * Get a setting from the settings API.
     *
     * @param mixed $option_name
     * @param mixed $default
     * @return mixed
     */
    public static function get_option( $option_name, $default = '' ) {
        if ( ! $option_name ) {
            return $default;
        }

        // Array value
        if ( strstr( $option_name, '[' ) ) {

            parse_str( $option_name, $option_array );

            // Option name is first key
            $option_name = current( array_keys( $option_array ) );

            // Get value
            $option_values = get_option( $option_name, '' );

            $key = key( $option_array[ $option_name ] );

            if ( isset( $option_values[ $key ] ) ) {
                $option_value = $option_values[ $key ];
            } else {
                $option_value = null;
            }
        } else {
            // Single value
            $option_value = get_option( $option_name, null );
        }

        if ( is_array( $option_value ) ) {
            $option_value = array_map( 'stripslashes', $option_value );
        } elseif ( ! is_null( $option_value ) ) {
            $option_value = stripslashes( $option_value );
        }

        return ( null === $option_value ) ? $default : $option_value;
    }

    /**
     * Output admin fields.
     *
     * Loops though the bdpay options array and outputs each field.
     *
     * @param array $options Opens array to output
     */
    public static function output_fields( $options ) {
        foreach ( $options as $value ) {
            if ( ! isset( $value['type'] ) ) {
                continue;
            }
            if ( ! isset( $value['id'] ) ) {
                $value['id'] = '';
            }
            if ( ! isset( $value['title'] ) ) {
                $value['title'] = isset( $value['name'] ) ? $value['name'] : '';
            }
            if ( ! isset( $value['class'] ) ) {
                $value['class'] = '';
            }
            if ( ! isset( $value['css'] ) ) {
                $value['css'] = '';
            }
            if ( ! isset( $value['default'] ) ) {
                $value['default'] = '';
            }
            if ( ! isset( $value['desc'] ) ) {
                $value['desc'] = '';
            }
            if ( ! isset( $value['desc_tip'] ) ) {
                $value['desc_tip'] = false;
            }
            if ( ! isset( $value['placeholder'] ) ) {
                $value['placeholder'] = '';
            }
            if ( ! isset( $value['suffix'] ) ) {
                $value['suffix'] = '';
            }

            // Custom attribute handling
            $custom_attributes = array();

            if ( ! empty( $value['custom_attributes'] ) && is_array( $value['custom_attributes'] ) ) {
                foreach ( $value['custom_attributes'] as $attribute => $attribute_value ) {
                    $custom_attributes[] = esc_attr( $attribute ) . '="' . esc_attr( $attribute_value ) . '"';
                }
            }

            // Description handling
            $field_description = self::get_field_description( $value );
            $description       = $field_description['description'];
            $tooltip_html      = $field_description['tooltip_html'];

            // Switch based on type
            switch ( $value['type'] ) {

                // Section Titles
                case 'title':
                    if ( ! empty( $value['title'] ) ) {
                        echo '<h2>' . esc_html( $value['title'] ) . '</h2>';
                    }
                    if ( ! empty( $value['desc'] ) ) {
                        echo '<div id="' . esc_attr( sanitize_title( $value['id'] ) ) . '-description">';
                        echo wp_kses_post( wpautop( wptexturize( $value['desc'] ) ) );
                        echo '</div>';
                    }
                    echo '<table class="form-table">' . "\n\n";
                    if ( ! empty( $value['id'] ) ) {
                        do_action( 'bdpay_settings_' . sanitize_title( $value['id'] ) );
                    }
                    break;

                // Section Ends
                case 'sectionend':
                    if ( ! empty( $value['id'] ) ) {
                        do_action( 'bdpay_settings_' . sanitize_title( $value['id'] ) );
                    }
                    echo '</table>';
                    if ( ! empty( $value['id'] ) ) {
                        do_action( 'bdpay_settings_' . sanitize_title( $value['id'] ) . '_end' );
                    }
                    break;

                // Standard text inputs and subtypes like 'number'
                case 'text':
                case 'password':
                case 'datetime':
                case 'datetime-local':
                case 'date':
                case 'month':
                case 'time':
                case 'week':
                case 'number':
                case 'email':
                case 'url':
                case 'tel':
                    $option_value = self::get_option( $value['id'], $value['default'] );

                    ?><tr valign="top">
                        <th scope="row" class="titledesc">
                            <label for="<?php echo esc_attr( $value['id'] ); ?>"><?php echo esc_html( $value['title'] ); ?> <?php echo $tooltip_html; // WPCS: XSS ok. ?></label>
                        </th>
                        <td class="forminp forminp-<?php echo esc_attr( sanitize_title( $value['type'] ) ); ?>">
                            <input
                                name="<?php echo esc_attr( $value['id'] ); ?>"
                                id="<?php echo esc_attr( $value['id'] ); ?>"
                                type="<?php echo esc_attr( $value['type'] ); ?>"
                                style="<?php echo esc_attr( $value['css'] ); ?>"
                                value="<?php echo esc_attr( $option_value ); ?>"
                                class="<?php echo esc_attr( $value['class'] ); ?>"
                                placeholder="<?php echo esc_attr( $value['placeholder'] ); ?>"
                                <?php echo implode( ' ', $custom_attributes ); // WPCS: XSS ok. ?>
                                /><?php echo esc_html( $value['suffix'] ); ?> <?php echo $description; // WPCS: XSS ok. ?>
                        </td>
                    </tr>
                    <?php
                    break;

                // Textarea
                case 'textarea':
                    $option_value = self::get_option( $value['id'], $value['default'] );

                    ?>
                    <tr valign="top">
                        <th scope="row" class="titledesc">
                            <label for="<?php echo esc_attr( $value['id'] ); ?>"><?php echo esc_html( $value['title'] ); ?> <?php echo $tooltip_html; // WPCS: XSS ok. ?></label>
                        </th>
                        <td class="forminp forminp-<?php echo esc_attr( sanitize_title( $value['type'] ) ); ?>">
                            <?php echo $description; // WPCS: XSS ok. ?>

                            <textarea
                                name="<?php echo esc_attr( $value['id'] ); ?>"
                                id="<?php echo esc_attr( $value['id'] ); ?>"
                                style="<?php echo esc_attr( $value['css'] ); ?>"
                                class="<?php echo esc_attr( $value['class'] ); ?>"
                                placeholder="<?php echo esc_attr( $value['placeholder'] ); ?>"
                                <?php echo implode( ' ', $custom_attributes ); // WPCS: XSS ok. ?>
                                ><?php echo esc_textarea( $option_value ); // WPCS: XSS ok. ?></textarea>
                        </td>
                    </tr>
                    <?php
                    break;

                // Checkbox input
                case 'checkbox':
                    $option_value     = self::get_option( $value['id'], $value['default'] );
                    $visibility_class = array();

                    if ( ! isset( $value['hide_if_checked'] ) ) {
                        $value['hide_if_checked'] = false;
                    }
                    if ( ! isset( $value['show_if_checked'] ) ) {
                        $value['show_if_checked'] = false;
                    }
                    if ( 'yes' === $value['hide_if_checked'] || 'yes' === $value['show_if_checked'] ) {
                        $visibility_class[] = 'hidden_option';
                    }
                    if ( 'option' === $value['hide_if_checked'] ) {
                        $visibility_class[] = 'hide_options_if_checked';
                    }
                    if ( 'option' === $value['show_if_checked'] ) {
                        $visibility_class[] = 'show_options_if_checked';
                    }

                    if ( ! isset( $value['checkboxgroup'] ) || 'start' === $value['checkboxgroup'] ) {
                        ?>
                            <tr valign="top" class="<?php echo esc_attr( implode( ' ', $visibility_class ) ); ?>">
                                <th scope="row" class="titledesc"><?php echo esc_html( $value['title'] ); ?></th>
                                <td class="forminp forminp-checkbox">
                                    <fieldset>
                        <?php
                    } else {
                        ?>
                            <fieldset class="<?php echo esc_attr( implode( ' ', $visibility_class ) ); ?>">
                        <?php
                    }

                    ?>
                        <legend class="screen-reader-text"><span><?php echo esc_html( $value['title'] ); ?></span></legend>
                        <label for="<?php echo esc_attr( $value['id'] ); ?>">
                        <input
                            name="<?php echo esc_attr( $value['id'] ); ?>"
                            id="<?php echo esc_attr( $value['id'] ); ?>"
                            type="checkbox"
                            class="<?php echo esc_attr( isset( $value['class'] ) ? $value['class'] : '' ); ?>"
                            value="1"
                            <?php checked( $option_value, 'yes' ); ?>
                            <?php echo implode( ' ', $custom_attributes ); // WPCS: XSS ok. ?>
                        /> <?php echo $description; // WPCS: XSS ok. ?>
                        </label> <?php echo $tooltip_html; // WPCS: XSS ok. ?>
                    <?php

                    if ( ! isset( $value['checkboxgroup'] ) || 'end' === $value['checkboxgroup'] ) {
                        ?>
                                    </fieldset>
                                </td>
                            </tr>
                        <?php
                    } else {
                        ?>
                            </fieldset>
                        <?php
                    }
                    break;

                default:
                    do_action( 'bdpay_admin_field_' . $value['type'], $value );
                    break;
            }
        }
    }

    /**
     * Helper function to get the formatted description and tip HTML for a given form field. Plugins can call this
     * when implementing their own custom settings types.
     *
     * @param  array $value The form field value array
     * @return array The description and tip as a 2 element array
     */
    public static function get_field_description( $value ) {
        $description  = '';
        $tooltip_html = '';

        if ( true === $value['desc_tip'] ) {
            $tooltip_html = $value['desc'];
        } elseif ( ! empty( $value['desc_tip'] ) ) {
            $description  = $value['desc'];
            $tooltip_html = $value['desc_tip'];
        } elseif ( ! empty( $value['desc'] ) ) {
            $description = $value['desc'];
        }

        if ( $description && in_array( $value['type'], array( 'textarea', 'radio' ), true ) ) {
            $description = '<p style="margin-top:0">' . wp_kses_post( $description ) . '</p>';
        } elseif ( $description && in_array( $value['type'], array( 'checkbox' ), true ) ) {
            $description = wp_kses_post( $description );
        } elseif ( $description ) {
            $description = '<span class="description">' . wp_kses_post( $description ) . '</span>';
        }

        if ( $tooltip_html && in_array( $value['type'], array( 'checkbox' ), true ) ) {
            $tooltip_html = '<p class="description">' . $tooltip_html . '</p>';
        } elseif ( $tooltip_html ) {
            $tooltip_html = bdpay_help_tip( $tooltip_html );
        }

        return array(
            'description'  => $description,
            'tooltip_html' => $tooltip_html,
        );
    }

    /**
     * Save admin fields.
     *
     * Loops though the bdpay options array and outputs each field.
     *
     * @param array $options Options array to output
     * @param array $data    Optional. Data to use for saving. Defaults to $_POST.
     * @return bool
     */
    public static function save_fields( $options, $data = null ) {
        if ( is_null( $data ) ) {
            $data = $_POST; // WPCS: input var okay, CSRF ok.
        }
        if ( empty( $data ) ) {
            return false;
        }

        // Options to update will be stored here and saved later.
        $update_options   = array();
        $autoload_options = array();

        // Loop options and get values to save.
        foreach ( $options as $option ) {
            if ( ! isset( $option['id'] ) || ! isset( $option['type'] ) || ( isset( $option['is_option'] ) && false === $option['is_option'] ) ) {
                continue;
            }

            // Get posted value.
            if ( strstr( $option['id'], '[' ) ) {
                parse_str( $option['id'], $option_name_array );
                $option_name  = current( array_keys( $option_name_array ) );
                $setting_name = key( $option_name_array[ $option_name ] );
                $raw_value    = isset( $data[ $option_name ][ $setting_name ] ) ? wp_unslash( $data[ $option_name ][ $setting_name ] ) : null;
            } else {
                $option_name  = $option['id'];
                $setting_name = '';
                $raw_value    = isset( $data[ $option['id'] ] ) ? wp_unslash( $data[ $option['id'] ] ) : null;
            }

            // Format the value based on option type.
            switch ( $option['type'] ) {
                case 'checkbox':
                    $value = '1' === $raw_value || 'yes' === $raw_value ? 'yes' : 'no';
                    break;
                case 'textarea':
                    $value = wp_kses_post( trim( $raw_value ) );
                    break;
                case 'multiselect':
                case 'multi_select_countries':
                    $value = array_filter( array_map( 'bdpay_clean', (array) $raw_value ) );
                    break;
                case 'image_width':
                    $value = array();
                    if ( isset( $raw_value['width'] ) ) {
                        $value['width']  = bdpay_clean( $raw_value['width'] );
                        $value['height'] = bdpay_clean( $raw_value['height'] );
                        $value['crop']   = isset( $raw_value['crop'] ) ? 1 : 0;
                    } else {
                        $value['width']  = $option['default']['width'];
                        $value['height'] = $option['default']['height'];
                        $value['crop']   = $option['default']['crop'];
                    }
                    break;
                case 'select':
                    $allowed_values = empty( $option['options'] ) ? array() : array_map( 'strval', array_keys( $option['options'] ) );
                    if ( empty( $option['default'] ) && empty( $allowed_values ) ) {
                        $value = null;
                        break;
                    }
                    $default = ( empty( $option['default'] ) ? $allowed_values[0] : $option['default'] );
                    $value   = in_array( $raw_value, $allowed_values, true ) ? $raw_value : $default;
                    break;
                case 'relative_date_selector':
                    $value = bdpay_parse_relative_date_option( $raw_value );
                    break;
                default:
                    $value = bdpay_clean( $raw_value );
                    break;
            }

            /**
             * Sanitize the value of an option.
             *
             * @since 2.4.0
             */
            $value = apply_filters( 'bdpay_admin_settings_sanitize_option', $value, $option, $raw_value );

            /**
             * Sanitize the value of an option by option name.
             *
             * @since 2.4.0
             */
            $value = apply_filters( "bdpay_admin_settings_sanitize_option_$option_name", $value, $option, $raw_value );

            if ( is_null( $value ) ) {
                continue;
            }

            // Check if option is an array and handle that differently to single values.
            if ( $option_name && $setting_name ) {
                if ( ! isset( $update_options[ $option_name ] ) ) {
                    $update_options[ $option_name ] = get_option( $option_name, array() );
                }
                if ( ! is_array( $update_options[ $option_name ] ) ) {
                    $update_options[ $option_name ] = array();
                }
                $update_options[ $option_name ][ $setting_name ] = $value;
            } else {
                $update_options[ $option_name ] = $value;
            }

            $autoload_options[ $option_name ] = isset( $option['autoload'] ) ? (bool) $option['autoload'] : true;

            /**
             * Fire an action before saved.
             *
             * @deprecated 2.4.0 Use bdpay_update_option_{$option_name} instead.
             */
            do_action( 'bdpay_update_option', $option );
        }

        // Save all options in our array.
        foreach ( $update_options as $name => $value ) {
            update_option( $name, $value, $autoload_options[ $name ] ? 'yes' : 'no' );

            /**
             * Fire an action after saved.
             */
            do_action( 'bdpay_update_option_' . $name, $value );
        }

        return true;
    }
}
