<?php
/**
 * BDPay Email Notifications
 *
 * @package BDPay
 * @since 1.0.0
 */

defined( 'ABSPATH' ) || exit;

/**
 * BDPay_Emails Class.
 */
class BDPay_Emails {

    /**
     * Constructor.
     */
    public function __construct() {
        add_action( 'bdpay_payment_submitted', array( $this, 'payment_submitted_notification' ), 10, 2 );
        add_action( 'bdpay_payment_verified', array( $this, 'payment_verified_notification' ), 10, 2 );
        add_action( 'bdpay_payment_rejected', array( $this, 'payment_rejected_notification' ), 10, 2 );
    }

    /**
     * Send notification when payment is submitted.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     */
    public function payment_submitted_notification( $order, $transaction ) {
        if ( 'yes' !== get_option( 'bdpay_enable_notifications', 'yes' ) ) {
            return;
        }

        // Send to admin
        $this->send_admin_notification( $order, $transaction, 'submitted' );
        
        // Send to customer
        $this->send_customer_notification( $order, $transaction, 'submitted' );
    }

    /**
     * Send notification when payment is verified.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     */
    public function payment_verified_notification( $order, $transaction ) {
        if ( 'yes' !== get_option( 'bdpay_enable_notifications', 'yes' ) ) {
            return;
        }

        // Send to customer
        $this->send_customer_notification( $order, $transaction, 'verified' );
    }

    /**
     * Send notification when payment is rejected.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     */
    public function payment_rejected_notification( $order, $transaction ) {
        if ( 'yes' !== get_option( 'bdpay_enable_notifications', 'yes' ) ) {
            return;
        }

        // Send to customer
        $this->send_customer_notification( $order, $transaction, 'rejected' );
    }

    /**
     * Send admin notification.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     * @param string $type
     */
    private function send_admin_notification( $order, $transaction, $type ) {
        $admin_email = get_option( 'admin_email' );
        
        if ( ! $admin_email ) {
            return;
        }

        $subject = $this->get_admin_email_subject( $type, $order );
        $message = $this->get_admin_email_message( $type, $order, $transaction );
        $headers = array( 'Content-Type: text/html; charset=UTF-8' );

        wp_mail( $admin_email, $subject, $message, $headers );
    }

    /**
     * Send customer notification.
     *
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     * @param string $type
     */
    private function send_customer_notification( $order, $transaction, $type ) {
        $customer_email = $order->get_billing_email();
        
        if ( ! $customer_email ) {
            return;
        }

        $subject = $this->get_customer_email_subject( $type, $order );
        $message = $this->get_customer_email_message( $type, $order, $transaction );
        $headers = array( 'Content-Type: text/html; charset=UTF-8' );

        wp_mail( $customer_email, $subject, $message, $headers );
    }

    /**
     * Get admin email subject.
     *
     * @param string $type
     * @param WC_Order $order
     * @return string
     */
    private function get_admin_email_subject( $type, $order ) {
        $site_name = get_bloginfo( 'name' );
        $order_number = $order->get_order_number();

        switch ( $type ) {
            case 'submitted':
                return sprintf( __( '[%s] New BDPay payment submission for order #%s', 'bdpay' ), $site_name, $order_number );
            default:
                return sprintf( __( '[%s] BDPay payment update for order #%s', 'bdpay' ), $site_name, $order_number );
        }
    }

    /**
     * Get customer email subject.
     *
     * @param string $type
     * @param WC_Order $order
     * @return string
     */
    private function get_customer_email_subject( $type, $order ) {
        $site_name = get_bloginfo( 'name' );
        $order_number = $order->get_order_number();

        switch ( $type ) {
            case 'submitted':
                return sprintf( __( '[%s] Payment details received for order #%s', 'bdpay' ), $site_name, $order_number );
            case 'verified':
                return sprintf( __( '[%s] Payment confirmed for order #%s', 'bdpay' ), $site_name, $order_number );
            case 'rejected':
                return sprintf( __( '[%s] Payment verification failed for order #%s', 'bdpay' ), $site_name, $order_number );
            default:
                return sprintf( __( '[%s] Payment update for order #%s', 'bdpay' ), $site_name, $order_number );
        }
    }

    /**
     * Get admin email message.
     *
     * @param string $type
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     * @return string
     */
    private function get_admin_email_message( $type, $order, $transaction ) {
        $site_name = get_bloginfo( 'name' );
        $order_number = $order->get_order_number();
        $order_url = admin_url( 'post.php?post=' . $order->get_id() . '&action=edit' );
        $payment_methods = BDPay_Core::get_payment_methods();
        $payment_method = $payment_methods[ $transaction->get_payment_method() ] ?? $transaction->get_payment_method();

        $message = '<html><body>';
        $message .= '<h2>' . sprintf( __( 'BDPay Payment Notification - %s', 'bdpay' ), $site_name ) . '</h2>';

        switch ( $type ) {
            case 'submitted':
                $message .= '<p>' . __( 'A new BDPay payment has been submitted and requires verification.', 'bdpay' ) . '</p>';
                break;
        }

        $message .= '<h3>' . __( 'Order Details:', 'bdpay' ) . '</h3>';
        $message .= '<ul>';
        $message .= '<li><strong>' . __( 'Order Number:', 'bdpay' ) . '</strong> #' . $order_number . '</li>';
        $message .= '<li><strong>' . __( 'Customer:', 'bdpay' ) . '</strong> ' . $order->get_billing_first_name() . ' ' . $order->get_billing_last_name() . '</li>';
        $message .= '<li><strong>' . __( 'Email:', 'bdpay' ) . '</strong> ' . $order->get_billing_email() . '</li>';
        $message .= '<li><strong>' . __( 'Total:', 'bdpay' ) . '</strong> ' . wc_price( $order->get_total() ) . '</li>';
        $message .= '</ul>';

        $message .= '<h3>' . __( 'Payment Details:', 'bdpay' ) . '</h3>';
        $message .= '<ul>';
        $message .= '<li><strong>' . __( 'Payment Method:', 'bdpay' ) . '</strong> ' . $payment_method . '</li>';
        $message .= '<li><strong>' . __( 'Transaction ID:', 'bdpay' ) . '</strong> ' . $transaction->get_transaction_id() . '</li>';
        $message .= '<li><strong>' . __( 'Sender Phone:', 'bdpay' ) . '</strong> ' . $transaction->get_sender_phone() . '</li>';
        $message .= '<li><strong>' . __( 'Amount:', 'bdpay' ) . '</strong> ' . wc_price( $transaction->get_amount() ) . '</li>';
        if ( $transaction->get_notes() ) {
            $message .= '<li><strong>' . __( 'Notes:', 'bdpay' ) . '</strong> ' . esc_html( $transaction->get_notes() ) . '</li>';
        }
        $message .= '</ul>';

        $message .= '<p><a href="' . $order_url . '" style="background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">' . __( 'View Order & Verify Payment', 'bdpay' ) . '</a></p>';

        $message .= '</body></html>';

        return $message;
    }

    /**
     * Get customer email message.
     *
     * @param string $type
     * @param WC_Order $order
     * @param BDPay_Transaction $transaction
     * @return string
     */
    private function get_customer_email_message( $type, $order, $transaction ) {
        $site_name = get_bloginfo( 'name' );
        $order_number = $order->get_order_number();
        $order_url = $order->get_view_order_url();
        $payment_methods = BDPay_Core::get_payment_methods();
        $payment_method = $payment_methods[ $transaction->get_payment_method() ] ?? $transaction->get_payment_method();

        $message = '<html><body>';
        $message .= '<h2>' . sprintf( __( 'Payment Update - %s', 'bdpay' ), $site_name ) . '</h2>';

        $message .= '<p>' . sprintf( __( 'Hello %s,', 'bdpay' ), $order->get_billing_first_name() ) . '</p>';

        switch ( $type ) {
            case 'submitted':
                $message .= '<p>' . __( 'Thank you for submitting your payment details. We have received your information and will verify your payment within 24 hours.', 'bdpay' ) . '</p>';
                break;
            case 'verified':
                $message .= '<p>' . __( 'Great news! Your payment has been verified and confirmed. Your order is now being processed.', 'bdpay' ) . '</p>';
                break;
            case 'rejected':
                $message .= '<p>' . __( 'We were unable to verify your payment details. Please check your information and try again, or contact our support team for assistance.', 'bdpay' ) . '</p>';
                break;
        }

        $message .= '<h3>' . __( 'Order Details:', 'bdpay' ) . '</h3>';
        $message .= '<ul>';
        $message .= '<li><strong>' . __( 'Order Number:', 'bdpay' ) . '</strong> #' . $order_number . '</li>';
        $message .= '<li><strong>' . __( 'Total:', 'bdpay' ) . '</strong> ' . wc_price( $order->get_total() ) . '</li>';
        $message .= '<li><strong>' . __( 'Payment Method:', 'bdpay' ) . '</strong> ' . $payment_method . '</li>';
        $message .= '<li><strong>' . __( 'Transaction ID:', 'bdpay' ) . '</strong> ' . $transaction->get_transaction_id() . '</li>';
        $message .= '</ul>';

        if ( 'rejected' === $type ) {
            $message .= '<p>' . __( 'If you believe this is an error, please contact our support team with your order number and transaction details.', 'bdpay' ) . '</p>';
        }

        $message .= '<p><a href="' . $order_url . '" style="background: #0073aa; color: white; padding: 10px 15px; text-decoration: none; border-radius: 3px;">' . __( 'View Order Details', 'bdpay' ) . '</a></p>';

        $message .= '<p>' . __( 'Thank you for your business!', 'bdpay' ) . '</p>';
        $message .= '<p>' . $site_name . '</p>';

        $message .= '</body></html>';

        return $message;
    }
}

new BDPay_Emails();
